#!/bin/bash

# Secure Pool Server Installation Script
# This script installs and configures the secure pool server with SSL support

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
INSTALL_DIR="/opt/mainnet"
SERVICE_NAME="mainnet-pool-server"
APP_USER="mainnet"
NGINX_SITES_DIR="/etc/nginx/sites-available"
NGINX_ENABLED_DIR="/etc/nginx/sites-enabled"
SSL_DIR="/etc/ssl/mainnet"

# Logging
LOG_FILE="/var/log/secure-pool-server-install.log"

# Create the log file if it doesn't exist
if [ ! -f "$LOG_FILE" ]; then
  sudo touch "$LOG_FILE"
  sudo chmod 644 "$LOG_FILE"
  sudo chown "$USER:$USER" "$LOG_FILE"  # or use 'root:root' if running as root
fi

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1" | tee -a "$LOG_FILE"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
}

# Function to check if running as root
check_root() {
    if [[ $EUID -ne 0 ]]; then
        print_error "This script must be run as root (use sudo)"
        exit 1
    fi
}

# Function to check system requirements
check_requirements() {
    print_status "Checking system requirements..."
    
    # Check OS
    if [[ ! -f /etc/os-release ]]; then
        print_error "Cannot determine OS version"
        exit 1
    fi
    
    . /etc/os-release
    if [[ "$ID" != "ubuntu" && "$ID" != "debian" ]]; then
        print_warning "This script is designed for Ubuntu/Debian. Proceeding anyway..."
    fi
    
    # Check architecture
    ARCH=$(uname -m)
    if [[ "$ARCH" != "x86_64" ]]; then
        print_warning "This script is optimized for x86_64 architecture"
    fi
    
    print_success "System requirements check completed"
}

# Function to install system dependencies
install_dependencies() {
    print_status "Installing system dependencies..."
    
    # Update package list
    apt-get update -y >> "$LOG_FILE" 2>&1
    
    # Install required packages
    apt-get install -y \
        curl \
        wget \
        git \
        nginx \
        certbot \
        python3-certbot-nginx \
        ufw \
        fail2ban \
        redis-server \
        build-essential \
        software-properties-common \
        apt-transport-https \
        ca-certificates \
        gnupg \
        lsb-release >> "$LOG_FILE" 2>&1
    
    print_success "System dependencies installed"
}

# Function to install Node.js
install_nodejs() {
    print_status "Installing Node.js 18..."
    
    # Add NodeSource repository
    curl -fsSL https://deb.nodesource.com/setup_18.x | bash - >> "$LOG_FILE" 2>&1
    
    # Install Node.js
    apt-get install -y nodejs >> "$LOG_FILE" 2>&1
    
    # Install PM2 globally
    npm install -g pm2 >> "$LOG_FILE" 2>&1
    
    # Verify installation
    NODE_VERSION=$(node --version)
    NPM_VERSION=$(npm --version)
    PM2_VERSION=$(pm2 --version)
    
    print_success "Node.js $NODE_VERSION, npm $NPM_VERSION, PM2 $PM2_VERSION installed"
}

# Function to create application user
create_app_user() {
    print_status "Creating dedicated application user '$APP_USER'..."

    # Create user if it doesn't exist
    if ! id "$APP_USER" &>/dev/null; then
        # Create system user with restricted shell and home directory
        useradd -r \
            -s /bin/false \
            -d "$INSTALL_DIR" \
            -c "Mainnet Pool Server" \
            -U \
            "$APP_USER"

        # Lock the account to prevent login
        passwd -l "$APP_USER" >/dev/null 2>&1

        # Create additional security groups
        groupadd -f mainnet-logs
        usermod -a -G mainnet-logs "$APP_USER"

        print_success "User '$APP_USER' created with restricted permissions"
    else
        print_warning "User '$APP_USER' already exists"
    fi

    # Set additional security restrictions
    print_status "Applying additional security restrictions..."

    # Prevent user from accessing other users' files
    chmod 750 "$INSTALL_DIR" 2>/dev/null || true

    # Set resource limits for the user
    cat >> /etc/security/limits.conf << EOF
# Mainnet pool server limits
$APP_USER soft nproc 1024
$APP_USER hard nproc 2048
$APP_USER soft nofile 4096
$APP_USER hard nofile 8192
$APP_USER soft memlock 64
$APP_USER hard memlock 64
EOF

    print_success "Security restrictions applied"
}

# Function to get user input securely
get_user_input() {
    print_status "Gathering configuration information..."
    
    # Domain name
    while [[ -z "$DOMAIN" ]]; do
        read -p "Enter your domain name (e.g., pool.example.com): " DOMAIN
        if [[ -z "$DOMAIN" ]]; then
            print_error "Domain name cannot be empty. Please enter your domain."
        fi
    done
    
    # Email for SSL certificate
    while [[ -z "$EMAIL" ]]; do
        read -p "Enter your email for SSL certificate: " EMAIL
        if [[ ! "$EMAIL" =~ ^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$ ]]; then
            print_error "Invalid email format. Please try again."
            EMAIL=""
        fi
    done
    
    # Private key (visible input for safe environments)
    while [[ -z "$PRIVATE_KEY" ]]; do
        read -p "Enter your Ethereum private key (0x...): " PRIVATE_KEY
        if [[ ! "$PRIVATE_KEY" =~ ^0x[a-fA-F0-9]{64}$ ]]; then
            print_error "Invalid private key format. Must be 0x followed by 64 hex characters."
            PRIVATE_KEY=""
        fi
    done
    
    # Base RPC URL
    read -p "Enter Base RPC URL [https://mainnet.base.org]: " BASE_RPC
    BASE_RPC=${BASE_RPC:-"https://mainnet.base.org"}
    
    # Skale RPC URL
    read -p "Enter Skale RPC URL [https://mainnet.skalenodes.com/v1/elated-tan-skat]: " SKALE_RPC
    SKALE_RPC=${SKALE_RPC:-"https://mainnet.skalenodes.com/v1/elated-tan-skat"}
    
    # Pool Storage Contract
    read -p "Enter Pool Storage Contract Address [******************************************]: " CONTRACT_ADDRESS
    CONTRACT_ADDRESS=${CONTRACT_ADDRESS:-"******************************************"}
    
    print_success "Configuration information collected"
}

# Function to install application
install_application() {
    print_status "Installing application to $INSTALL_DIR..."
    # Create installation directory
    mkdir -p "$INSTALL_DIR"
    
    # Copy application files
    cp -rfp . "$INSTALL_DIR/"
    
    # Create logs directory
    mkdir -p "$INSTALL_DIR/logs"
    
    # Set ownership with strict permissions
    chown -R "$APP_USER:$APP_USER" "$INSTALL_DIR"
    chmod -R 750 "$INSTALL_DIR"

    # Create restricted directories
    mkdir -p "$INSTALL_DIR"/{logs,tmp}
    chown "$APP_USER:mainnet-logs" "$INSTALL_DIR/logs"
    chmod 750 "$INSTALL_DIR/logs"
    chmod 700 "$INSTALL_DIR/tmp"

    # Install npm dependencies
    cd "$INSTALL_DIR"
    print_status "Installing npm dependencies in $INSTALL_DIR..."
    sudo -u "$APP_USER" npm install --production >> "$LOG_FILE" 2>&1
    
    print_success "Application installed to $INSTALL_DIR"
}

# Function to configure environment
configure_environment() {
    print_status "Configuring environment variables..."
    
    # Generate secure secrets
    ENCRYPTION_KEY=$(openssl rand -hex 32)
    MASTER_PASSWORD=$(openssl rand -base64 32)
    JWT_SECRET=$(openssl rand -base64 64)
    SESSION_SECRET=$(openssl rand -base64 64)
    REDIS_PASSWORD=$(openssl rand -base64 32)
    
    # Encrypt private key
    cd "$INSTALL_DIR"
    ENCRYPTED_PRIVATE_KEY=$(sudo -u mainnet node -e "
        const security = require('./src/config/security');
        const crypto = require('crypto');
        
        // Temporary encryption for setup
        const algorithm = 'aes-256-gcm';
        const key = Buffer.from('$ENCRYPTION_KEY', 'hex');
        const iv = crypto.randomBytes(16);
        const cipher = crypto.createCipher(algorithm, key, { iv });
        
        let encrypted = cipher.update('$PRIVATE_KEY', 'utf8', 'hex');
        encrypted += cipher.final('hex');
        const tag = cipher.getAuthTag();
        
        const combined = Buffer.concat([iv, tag, Buffer.from(encrypted, 'hex')]);
        console.log(combined.toString('base64'));
    ")
    
    # Create .env file
    cat > "$INSTALL_DIR/.env" << EOF
# Server Configuration
NODE_ENV=production
PORT=3000
HOST=0.0.0.0

# Security Configuration
ENCRYPTION_KEY=$ENCRYPTION_KEY
MASTER_PASSWORD=$MASTER_PASSWORD
JWT_SECRET=$JWT_SECRET
SESSION_SECRET=$SESSION_SECRET
ENCRYPTED_PRIVATE_KEY=$ENCRYPTED_PRIVATE_KEY

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
SLOW_DOWN_DELAY_AFTER=50
SLOW_DOWN_DELAY_MS=500

# Blockchain Configuration
BASE_RPC_URL=$BASE_RPC
SKALE_RPC_URL=$SKALE_RPC
POOL_STORAGE_CONTRACT=$CONTRACT_ADDRESS

# Gas Configuration
GAS_LIMIT=500000
GAS_PRICE_MULTIPLIER=1.2
MAX_GAS_PRICE=50000000000

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=$REDIS_PASSWORD

# Monitoring and Logging
LOG_LEVEL=info
LOG_MAX_SIZE=20m
LOG_MAX_FILES=14d
ENABLE_REQUEST_LOGGING=true

# Security Headers
CORS_ORIGIN=https://$DOMAIN
TRUSTED_PROXIES=1

# Health Check
HEALTH_CHECK_INTERVAL=30000
EOF
    
    # Set secure permissions for environment file
    chown "$APP_USER:$APP_USER" "$INSTALL_DIR/.env"
    chmod 600 "$INSTALL_DIR/.env"

    # Ensure no other users can read the directory
    chmod 750 "$INSTALL_DIR"
    
    print_success "Environment configured"
}

# Function to configure Redis
configure_redis() {
    print_status "Configuring Redis..."

    # Configure Redis with password
    sed -i "s/# requirepass foobared/requirepass $REDIS_PASSWORD/" /etc/redis/redis.conf

    # Enable Redis to start on boot
    systemctl enable redis-server
    systemctl restart redis-server

    print_success "Redis configured and started"
}

# Function to setup SSL certificate
setup_ssl() {
    print_status "Setting up SSL certificate for $DOMAIN..."

    # Stop nginx temporarily
    systemctl stop nginx 2>/dev/null || true

    # Get SSL certificate
    if certbot certonly --standalone \
        --non-interactive \
        --agree-tos \
        --email "$EMAIL" \
        -d "$DOMAIN" >> "$LOG_FILE" 2>&1; then
        print_success "SSL certificate obtained for $DOMAIN"
    else
        print_error "Failed to obtain SSL certificate"
        exit 1
    fi
}

# Function to configure Nginx
configure_nginx() {
    print_status "Configuring Nginx..."

    # Create Nginx configuration
    cat > "$NGINX_SITES_DIR/$SERVICE_NAME" << EOF
# Rate limiting zones
limit_req_zone \$binary_remote_addr zone=api:10m rate=10r/s;
limit_req_zone \$binary_remote_addr zone=join:10m rate=1r/s;
limit_conn_zone \$binary_remote_addr zone=conn_limit_per_ip:10m;

# Upstream configuration
upstream pool_server {
    server 127.0.0.1:3000;
    keepalive 32;
}

# HTTP server (redirect to HTTPS)
server {
    listen 80;
    server_name $DOMAIN;

    # Health check endpoint (allow HTTP for load balancers)
    location /health {
        proxy_pass http://pool_server;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        access_log off;
    }

    # Redirect all other traffic to HTTPS
    location / {
        return 301 https://\$server_name\$request_uri;
    }
}

# HTTPS server
server {
    listen 443 ssl http2;
    server_name $DOMAIN;

    # SSL configuration
    ssl_certificate /etc/letsencrypt/live/$DOMAIN/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/$DOMAIN/privkey.pem;
    ssl_session_timeout 1d;
    ssl_session_cache shared:SSL:50m;
    ssl_session_tickets off;

    # Modern configuration
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # Security headers
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload" always;
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    # Connection limits
    limit_conn conn_limit_per_ip 10;

    # Main application
    location / {
        limit_req zone=api burst=20 nodelay;

        proxy_pass http://pool_server;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;

        # Timeouts
        proxy_connect_timeout 5s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # Join endpoint with stricter rate limiting
    location /join {
        limit_req zone=join burst=5 nodelay;

        proxy_pass http://pool_server;
        proxy_http_version 1.1;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;

        # Timeouts for blockchain operations
        proxy_connect_timeout 5s;
        proxy_send_timeout 120s;
        proxy_read_timeout 120s;
    }

    # Health check
    location /health {
        proxy_pass http://pool_server;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        access_log off;
    }
}
EOF

    # Enable the site
    ln -sf "$NGINX_SITES_DIR/$SERVICE_NAME" "$NGINX_ENABLED_DIR/"

    # Remove default site
    rm -f "$NGINX_ENABLED_DIR/default"

    # Test Nginx configuration
    if nginx -t >> "$LOG_FILE" 2>&1; then
        print_success "Nginx configured successfully"
    else
        print_error "Nginx configuration test failed"
        exit 1
    fi
}

# Function to create systemd service
create_systemd_service() {
    print_status "Creating systemd service..."

    cat > "/etc/systemd/system/$SERVICE_NAME.service" << EOF
[Unit]
Description=Mainnet Pool Server
Documentation=https://github.com/your-repo/secure-pool-server
After=network.target redis-server.service
Wants=redis-server.service

[Service]
Type=forking
User=$APP_USER
Group=$APP_USER
WorkingDirectory=$INSTALL_DIR
Environment=NODE_ENV=production
ExecStart=/usr/bin/pm2 start $INSTALL_DIR/ecosystem.config.js --env production
ExecReload=/usr/bin/pm2 reload $INSTALL_DIR/ecosystem.config.js --env production
ExecStop=/usr/bin/pm2 stop $INSTALL_DIR/ecosystem.config.js
PIDFile=$INSTALL_DIR/.pm2/pm2.pid

# Enhanced security settings
NoNewPrivileges=true
PrivateTmp=true
PrivateDevices=true
ProtectSystem=strict
ProtectHome=true
ProtectKernelTunables=true
ProtectKernelModules=true
ProtectControlGroups=true
RestrictRealtime=true
RestrictSUIDSGID=true
RemoveIPC=true
ReadWritePaths=$INSTALL_DIR/logs $INSTALL_DIR/tmp
ReadOnlyPaths=$INSTALL_DIR
CapabilityBoundingSet=
AmbientCapabilities=

# Network restrictions
RestrictAddressFamilies=AF_INET AF_INET6 AF_UNIX
IPAddressDeny=any
IPAddressAllow=localhost
IPAddressAllow=10.0.0.0/8
IPAddressAllow=**********/12
IPAddressAllow=***********/16

# Resource limits
LimitNOFILE=4096
LimitNPROC=1024
LimitAS=1G
LimitDATA=512M
LimitSTACK=8M
LimitCORE=0

# Restart policy
Restart=always
RestartSec=10
StartLimitInterval=60s
StartLimitBurst=3

[Install]
WantedBy=multi-user.target
EOF

    # Reload systemd and enable service
    systemctl daemon-reload
    systemctl enable "$SERVICE_NAME"

    print_success "Systemd service created and enabled"
}

# Function to configure firewall
configure_firewall() {
    print_status "Configuring firewall..."

    # Reset UFW to defaults
    ufw --force reset >> "$LOG_FILE" 2>&1

    # Set default policies
    ufw default deny incoming >> "$LOG_FILE" 2>&1
    ufw default allow outgoing >> "$LOG_FILE" 2>&1

    # Allow SSH (be careful not to lock yourself out)
    ufw allow ssh >> "$LOG_FILE" 2>&1

    # Allow HTTP and HTTPS
    ufw allow 80/tcp >> "$LOG_FILE" 2>&1
    ufw allow 443/tcp >> "$LOG_FILE" 2>&1

    # Enable firewall
    ufw --force enable >> "$LOG_FILE" 2>&1

    print_success "Firewall configured"
}

# Function to configure fail2ban
configure_fail2ban() {
    print_status "Configuring fail2ban..."

    # Create custom jail for our application
    cat > "/etc/fail2ban/jail.d/$SERVICE_NAME.conf" << EOF
[nginx-http-auth]
enabled = true
port = http,https
logpath = /var/log/nginx/error.log

[nginx-limit-req]
enabled = true
port = http,https
logpath = /var/log/nginx/error.log
maxretry = 10

[sshd]
enabled = true
port = ssh
logpath = /var/log/auth.log
maxretry = 5
bantime = 3600
EOF

    # Restart fail2ban
    systemctl restart fail2ban
    systemctl enable fail2ban

    print_success "Fail2ban configured"
}

# Function to start services
start_services() {
    print_status "Starting services..."

    # Start Redis
    systemctl start redis-server

    # Start the application
    cd "$INSTALL_DIR"
    sudo -u "$APP_USER" pm2 start ecosystem.config.js --env production 2>&1 | tee -a "$LOG_FILE"
    sudo -u "$APP_USER" pm2 save 2>&1 | tee -a "$LOG_FILE"

    # Start systemd service
    systemctl start "$SERVICE_NAME"

    # Start Nginx
    systemctl start nginx
    systemctl enable nginx

    print_success "All services started"
}

# Function to setup SSL certificate renewal
setup_ssl_renewal() {
    print_status "Setting up SSL certificate auto-renewal..."

    # Create renewal hook to reload nginx
    cat > "/etc/letsencrypt/renewal-hooks/deploy/nginx-reload.sh" << EOF
#!/bin/bash
systemctl reload nginx
EOF

    chmod +x "/etc/letsencrypt/renewal-hooks/deploy/nginx-reload.sh"

    # Test renewal (dry run)
    certbot renew --dry-run >> "$LOG_FILE" 2>&1

    print_success "SSL auto-renewal configured"
}

# Function to perform health check
perform_health_check() {
    print_status "Performing health check..."

    # Wait for services to start
    sleep 10

    # Test local connection
    if curl -f http://localhost:3000/health >> "$LOG_FILE" 2>&1; then
        print_success "Local health check passed"
    else
        print_error "Local health check failed"
        return 1
    fi

    # Test HTTP redirect
    if curl -I "http://$DOMAIN" 2>/dev/null | grep -q "301\|302"; then
        print_success "HTTP to HTTPS redirect working"
    else
        print_warning "HTTP redirect may not be working properly"
    fi

    # Test HTTPS connection
    if curl -f "https://$DOMAIN/health" >> "$LOG_FILE" 2>&1; then
        print_success "HTTPS health check passed"
    else
        print_error "HTTPS health check failed"
        return 1
    fi

    # Test unauthorized endpoint
    RESPONSE=$(curl -s "https://$DOMAIN/" | grep -o "You are not authorized")
    if [[ "$RESPONSE" == "You are not authorized" ]]; then
        print_success "Main endpoint security check passed"
    else
        print_warning "Main endpoint response unexpected"
    fi

    return 0
}

# Function to display final information
display_final_info() {
    echo
    echo "=============================================="
    echo -e "${GREEN}🎉 INSTALLATION COMPLETED SUCCESSFULLY! 🎉${NC}"
    echo "=============================================="
    echo
    echo -e "${BLUE}📍 Installation Details:${NC}"
    echo "   • Installation Directory: $INSTALL_DIR"
    echo "   • Service Name: $SERVICE_NAME"
    echo "   • Domain: $DOMAIN"
    echo "   • SSL Certificate: Enabled"
    echo
    echo -e "${BLUE}🔗 Access URLs:${NC}"
    echo "   • Main Page: https://$DOMAIN/"
    echo "   • Health Check: https://$DOMAIN/health"
    echo "   • Join Endpoint: https://$DOMAIN/join (POST)"
    echo
    echo -e "${BLUE}🛠️ Management Commands:${NC}"
    echo "   • Check Status: systemctl status $SERVICE_NAME"
    echo "   • View Logs: journalctl -u $SERVICE_NAME -f"
    echo "   • Restart Service: systemctl restart $SERVICE_NAME"
    echo "   • PM2 Status: sudo -u $APP_USER pm2 status"
    echo "   • PM2 Logs: sudo -u $APP_USER pm2 logs"
    echo
    echo -e "${BLUE}📁 Important Files:${NC}"
    echo "   • Environment: $INSTALL_DIR/.env"
    echo "   • Logs: $INSTALL_DIR/logs/"
    echo "   • Nginx Config: $NGINX_SITES_DIR/$SERVICE_NAME"
    echo "   • Service Config: /etc/systemd/system/$SERVICE_NAME.service"
    echo
    echo -e "${YELLOW}⚠️  Security Reminders:${NC}"
    echo "   • Your private key is encrypted and stored securely"
    echo "   • Firewall is configured to allow only necessary ports"
    echo "   • Fail2ban is active for intrusion prevention"
    echo "   • SSL certificate will auto-renew"
    echo "   • Regular security updates are recommended"
    echo
    echo -e "${GREEN}✅ Your secure pool server is now running!${NC}"
    echo
}

# Main installation function
main() {
    echo
    echo "=============================================="
    echo -e "${BLUE}🔒 Secure Pool Server Installation Script${NC}"
    echo "=============================================="
    echo
    echo "This script will install and configure a production-ready"
    echo "secure pool server with SSL support and automatic startup."
    echo
    echo -e "${YELLOW}⚠️  This script requires root privileges and will:${NC}"
    echo "   • Install system dependencies (Node.js, Nginx, Redis, etc.)"
    echo "   • Create system user and service"
    echo "   • Configure SSL certificate with Let's Encrypt"
    echo "   • Set up firewall and security measures"
    echo "   • Install application to $INSTALL_DIR"
    echo
    read -p "Do you want to continue? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "Installation cancelled."
        exit 0
    fi

    # Start installation
    echo "Starting installation..." | tee "$LOG_FILE"
    echo "Installation log: $LOG_FILE"
    echo

    # Run installation steps
    check_root
    check_requirements
    install_dependencies
    install_nodejs
    create_app_user
    get_user_input
    install_application
    configure_environment
    configure_redis
    setup_ssl
    configure_nginx
    create_systemd_service
    # configure_firewall
    configure_fail2ban
    start_services
    setup_ssl_renewal

    # Perform health check
    if perform_health_check; then
        display_final_info
    else
        print_error "Health check failed. Please check the logs and configuration."
        echo "Log file: $LOG_FILE"
        exit 1
    fi
}

# Error handling
trap 'print_error "Installation failed at line $LINENO. Check $LOG_FILE for details."' ERR

# Run main function if script is executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
