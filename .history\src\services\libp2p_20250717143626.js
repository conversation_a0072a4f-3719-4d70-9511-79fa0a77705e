// Dynamic imports for ES modules
let createLibp2p, ping, tcp, webSockets, noise, yamux, circuitRelayTransport, identify, multiaddr;

async function loadLibp2pModules() {
  if (!createLibp2p) {
    const libp2pModule = await import('libp2p');
    createLibp2p = libp2pModule.createLibp2p;

    const pingModule = await import('@libp2p/ping');
    ping = pingModule.ping;

    const tcpModule = await import('@libp2p/tcp');
    tcp = tcpModule.tcp;

    const webSocketsModule = await import('@libp2p/websockets');
    webSockets = webSocketsModule.webSockets;

    const noiseModule = await import('@libp2p/noise');
    noise = noiseModule.noise;

    const yamuxModule = await import('@chainsafe/libp2p-yamux');
    yamux = yamuxModule.yamux;

    const circuitRelayModule = await import('@libp2p/circuit-relay-v2');
    circuitRelayTransport = circuitRelayModule.circuitRelayTransport;

    const identifyModule = await import('@libp2p/identify');
    identify = identifyModule.identify;

    const multiaddrModule = await import('@multiformats/multiaddr');
    multiaddr = multiaddrModule.multiaddr;
  }
}

const logger = require('../utils/logger');
const config = require('../config/environment');

class LibP2PService {
  constructor() {
    this.node = null;
    this.relayAddress = '/dns/relay.dev.fx.land/tcp/4001/p2p/12D3KooWDRrBaAfPwsGJivBoUw5fE7ZpDiyfUjqgiURq2DEcL835/p2p-circuit';
    this.pingTimeout = 10000; // 10 seconds timeout
    this.maxRetries = 3;
    this.isInitialized = false;
  }

  async initialize() {
    if (this.isInitialized) {
      return;
    }

    try {
      logger.info('Loading libp2p modules...');
      await loadLibp2pModules();

      logger.info('Initializing libp2p node...');

      this.node = await createLibp2p({
        addresses: {
          listen: []
        },
        transports: [
          tcp(),
          webSockets(),
          circuitRelayTransport({
            discoverRelays: 1
          })
        ],
        connectionEncryption: [noise()],
        streamMuxers: [yamux()],
        services: {
          ping: ping({
            protocolPrefix: 'ipfs',
            maxInboundStreams: 32,
            maxOutboundStreams: 64,
            timeout: this.pingTimeout
          }),
          identify: identify()
        },
        connectionManager: {
          maxConnections: 100,
          minConnections: 0
        }
      });

      await this.node.start();
      this.isInitialized = true;

      logger.info('LibP2P node initialized successfully', {
        peerId: this.node.peerId.toString(),
        addresses: this.node.getMultiaddrs().map(addr => addr.toString())
      });

    } catch (error) {
      logger.error('Failed to initialize libp2p node:', error);
      throw new Error('LibP2P initialization failed');
    }
  }

  async shutdown() {
    if (this.node && this.isInitialized) {
      try {
        await this.node.stop();
        this.isInitialized = false;
        logger.info('LibP2P node stopped successfully');
      } catch (error) {
        logger.error('Error stopping libp2p node:', error);
      }
    }
  }

  /**
   * Validates if a peer ID is reachable through the relay
   * @param {string} peerId - The peer ID to ping
   * @returns {Promise<boolean>} - True if peer is reachable, false otherwise
   */
  async validatePeerConnectivity(peerId) {
    if (!this.isInitialized) {
      await this.initialize();
    }

    const startTime = Date.now();
    let lastError = null;

    try {
      // Validate peer ID format
      if (!peerId || typeof peerId !== 'string') {
        throw new Error('Invalid peer ID format');
      }

      // Clean peer ID (remove any prefixes)
      const cleanPeerId = peerId.replace(/^\/p2p\//, '').replace(/^12D3/, '12D3');
      
      // Construct the full multiaddr through the relay
      const targetMultiaddr = `${this.relayAddress}/p2p/${cleanPeerId}`;
      
      logger.info('Attempting to ping peer through relay', {
        peerId: cleanPeerId.substring(0, 8) + '...',
        relay: this.relayAddress,
        targetAddr: targetMultiaddr
      });

      // Parse the multiaddr
      let targetAddr;
      try {
        targetAddr = multiaddr(targetMultiaddr);
      } catch (error) {
        throw new Error(`Invalid multiaddr format: ${error.message}`);
      }

      // Attempt to ping with retries
      for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
        try {
          logger.debug(`Ping attempt ${attempt}/${this.maxRetries}`, {
            peerId: cleanPeerId.substring(0, 8) + '...',
            attempt
          });

          // Try to dial and ping the peer
          const connection = await this.node.dial(targetAddr);
          
          if (connection) {
            // Perform ping
            const pingResult = await this.node.services.ping.ping(connection.remotePeer);
            
            if (pingResult && pingResult > 0) {
              const duration = Date.now() - startTime;
              
              logger.info('Peer ping successful', {
                peerId: cleanPeerId.substring(0, 8) + '...',
                latency: pingResult,
                duration: `${duration}ms`,
                attempt
              });

              // Close the connection
              await connection.close();
              
              return true;
            }
          }
        } catch (error) {
          lastError = error;
          logger.warn(`Ping attempt ${attempt} failed`, {
            peerId: cleanPeerId.substring(0, 8) + '...',
            attempt,
            error: error.message
          });

          // Wait before retry (exponential backoff)
          if (attempt < this.maxRetries) {
            const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000);
            await new Promise(resolve => setTimeout(resolve, delay));
          }
        }
      }

      // All attempts failed
      const duration = Date.now() - startTime;
      
      logger.warn('Peer ping failed after all attempts', {
        peerId: cleanPeerId.substring(0, 8) + '...',
        attempts: this.maxRetries,
        duration: `${duration}ms`,
        lastError: lastError?.message
      });

      return false;

    } catch (error) {
      const duration = Date.now() - startTime;
      
      logger.error('Peer connectivity validation error', {
        peerId: peerId.substring(0, 8) + '...',
        error: error.message,
        duration: `${duration}ms`
      });

      return false;
    }
  }

  /**
   * Health check for the libp2p service
   * @returns {Promise<Object>} - Health status
   */
  async healthCheck() {
    try {
      if (!this.isInitialized) {
        return {
          status: 'unhealthy',
          error: 'LibP2P node not initialized'
        };
      }

      const connections = this.node.getConnections();
      const peers = this.node.getPeers();

      return {
        status: 'healthy',
        peerId: this.node.peerId.toString(),
        connections: connections.length,
        peers: peers.length,
        addresses: this.node.getMultiaddrs().map(addr => addr.toString())
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error.message
      };
    }
  }

  /**
   * Get connection statistics
   * @returns {Object} - Connection statistics
   */
  getStats() {
    if (!this.isInitialized || !this.node) {
      return {
        initialized: false,
        connections: 0,
        peers: 0
      };
    }

    try {
      const connections = this.node.getConnections();
      const peers = this.node.getPeers();

      return {
        initialized: true,
        peerId: this.node.peerId.toString(),
        connections: connections.length,
        peers: peers.length,
        addresses: this.node.getMultiaddrs().map(addr => addr.toString()),
        connectionDetails: connections.map(conn => ({
          remotePeer: conn.remotePeer.toString(),
          status: conn.status,
          direction: conn.direction,
          timeline: conn.timeline
        }))
      };
    } catch (error) {
      logger.error('Error getting libp2p stats:', error);
      return {
        initialized: true,
        error: error.message
      };
    }
  }

  /**
   * Force reconnection to relay
   * @returns {Promise<boolean>} - Success status
   */
  async reconnectToRelay() {
    try {
      logger.info('Attempting to reconnect to relay...');
      
      const relayAddr = multiaddr(this.relayAddress);
      const connection = await this.node.dial(relayAddr);
      
      if (connection) {
        logger.info('Successfully reconnected to relay');
        return true;
      }
      
      return false;
    } catch (error) {
      logger.error('Failed to reconnect to relay:', error);
      return false;
    }
  }
}

export default new LibP2PService();
