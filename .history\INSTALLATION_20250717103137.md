# 🚀 Secure Pool Server Installation Guide

This guide will help you install and configure the Secure Pool Server on a fresh Ubuntu/Debian server.

## 📋 Prerequisites

### Server Requirements
- **OS**: Ubuntu 20.04+ or Debian 11+ (recommended)
- **RAM**: Minimum 2GB, recommended 4GB+
- **Storage**: Minimum 20GB SSD
- **CPU**: 2+ cores recommended
- **Network**: Public IP address with domain name

### Before Installation
1. **Domain Setup**: Point your domain's A record to your server's IP address
2. **SSH Access**: Ensure you have root or sudo access to the server
3. **Firewall**: The script will configure UFW firewall automatically
4. **Private Key**: Have your Ethereum private key ready (will be encrypted during installation)

## 🔧 Quick Installation

### Step 1: Download and Prepare
```bash
# Clone or download the repository
git clone <repository-url>
cd secure-pool-server

# Make the installation script executable
chmod +x install.sh
```

### Step 2: Run Installation
```bash
# Run as root (required for system configuration)
sudo ./install.sh
```

### Step 3: Follow Interactive Prompts

The installation script will ask for the following information:

1. **Domain Name**: Your fully qualified domain name (e.g., `pool.example.com`)
2. **Email Address**: For SSL certificate registration with Let's Encrypt
3. **Private Key**: Your Ethereum private key (input will be hidden)
4. **RPC URLs**: Base and Skale network endpoints (defaults provided)
5. **Contract Address**: Pool storage contract address (default provided)

### Example Installation Session
```
🔒 Secure Pool Server Installation Script
==============================================

This script will install and configure a production-ready
secure pool server with SSL support and automatic startup.

⚠️  This script requires root privileges and will:
   • Install system dependencies (Node.js, Nginx, Redis, etc.)
   • Create dedicated 'mainnet' user with restricted permissions
   • Configure SSL certificate with Let's Encrypt
   • Set up firewall and security measures
   • Install application to /opt/mainnet

Do you want to continue? (y/N): y

[INFO] Starting installation...
[INFO] Checking system requirements...
[SUCCESS] System requirements check completed
[INFO] Installing system dependencies...
[SUCCESS] System dependencies installed
[INFO] Installing Node.js 18...
[SUCCESS] Node.js v18.x.x, npm x.x.x, PM2 x.x.x installed
[INFO] Creating application user...
[SUCCESS] User 'poolserver' created
[INFO] Gathering configuration information...

Enter your domain name (e.g., pool.example.com): pool.example.com
Enter your email for SSL certificate: <EMAIL>
Enter your Ethereum private key (0x...): ********************************
Enter Base RPC URL [https://mainnet.base.org]: 
Enter Skale RPC URL [https://mainnet.skalenodes.com/v1/elated-tan-skat]: 
Enter Pool Storage Contract Address [******************************************]: 

[SUCCESS] Configuration information collected
[INFO] Installing application to /opt/secure-pool-server...
[SUCCESS] Application installed to /opt/secure-pool-server
[INFO] Configuring environment variables...
[SUCCESS] Environment configured
[INFO] Configuring Redis...
[SUCCESS] Redis configured and started
[INFO] Setting up SSL certificate for pool.example.com...
[SUCCESS] SSL certificate obtained for pool.example.com
[INFO] Configuring Nginx...
[SUCCESS] Nginx configured successfully
[INFO] Creating systemd service...
[SUCCESS] Systemd service created and enabled
[INFO] Configuring firewall...
[SUCCESS] Firewall configured
[INFO] Configuring fail2ban...
[SUCCESS] Fail2ban configured
[INFO] Starting services...
[SUCCESS] All services started
[INFO] Setting up SSL certificate auto-renewal...
[SUCCESS] SSL auto-renewal configured
[INFO] Performing health check...
[SUCCESS] Local health check passed
[SUCCESS] HTTP to HTTPS redirect working
[SUCCESS] HTTPS health check passed
[SUCCESS] Main endpoint security check passed

==============================================
🎉 INSTALLATION COMPLETED SUCCESSFULLY! 🎉
==============================================

📍 Installation Details:
   • Installation Directory: /opt/secure-pool-server
   • Service Name: secure-pool-server
   • Domain: pool.example.com
   • SSL Certificate: Enabled

🔗 Access URLs:
   • Main Page: https://pool.example.com/
   • Health Check: https://pool.example.com/health
   • Join Endpoint: https://pool.example.com/join (POST)

🛠️ Management Commands:
   • Check Status: systemctl status secure-pool-server
   • View Logs: journalctl -u secure-pool-server -f
   • Restart Service: systemctl restart secure-pool-server
   • PM2 Status: sudo -u poolserver pm2 status
   • PM2 Logs: sudo -u poolserver pm2 logs

📁 Important Files:
   • Environment: /opt/secure-pool-server/.env
   • Logs: /opt/secure-pool-server/logs/
   • Nginx Config: /etc/nginx/sites-available/secure-pool-server
   • Service Config: /etc/systemd/system/secure-pool-server.service

⚠️  Security Reminders:
   • Your private key is encrypted and stored securely
   • Firewall is configured to allow only necessary ports
   • Fail2ban is active for intrusion prevention
   • SSL certificate will auto-renew
   • Regular security updates are recommended

✅ Your secure pool server is now running!
```

## 🔍 Post-Installation Verification

### Test the Installation
```bash
# Test health endpoint
curl https://your-domain.com/health

# Test main page (should return "You are not authorized")
curl https://your-domain.com/

# Test join endpoint (should return validation errors)
curl -X POST https://your-domain.com/join \
  -H "Content-Type: application/json" \
  -d '{}'
```

### Check Service Status
```bash
# Check main service
systemctl status secure-pool-server

# Check Nginx
systemctl status nginx

# Check Redis
systemctl status redis-server

# Check PM2 processes
sudo -u poolserver pm2 status
```

### View Logs
```bash
# Application logs
sudo -u poolserver pm2 logs

# System service logs
journalctl -u secure-pool-server -f

# Nginx logs
tail -f /var/log/nginx/access.log
tail -f /var/log/nginx/error.log

# Application logs
tail -f /opt/secure-pool-server/logs/combined-*.log
```

## 🛠️ Management Commands

### Service Management
```bash
# Start/Stop/Restart service
systemctl start secure-pool-server
systemctl stop secure-pool-server
systemctl restart secure-pool-server

# Enable/Disable auto-start
systemctl enable secure-pool-server
systemctl disable secure-pool-server
```

### PM2 Management
```bash
# Switch to poolserver user
sudo -u poolserver -i

# PM2 commands
pm2 status
pm2 logs
pm2 restart all
pm2 reload all
pm2 stop all
pm2 delete all
```

### SSL Certificate Management
```bash
# Check certificate status
certbot certificates

# Renew certificates (dry run)
certbot renew --dry-run

# Force renewal
certbot renew --force-renewal
```

## 🔧 Configuration Updates

### Environment Variables
Edit `/opt/secure-pool-server/.env` to update configuration:
```bash
sudo nano /opt/secure-pool-server/.env
sudo systemctl restart secure-pool-server
```

### Nginx Configuration
Edit `/etc/nginx/sites-available/secure-pool-server`:
```bash
sudo nano /etc/nginx/sites-available/secure-pool-server
sudo nginx -t
sudo systemctl reload nginx
```

## 🚨 Troubleshooting

### Common Issues

1. **SSL Certificate Failed**
   - Ensure domain points to server IP
   - Check firewall allows port 80/443
   - Verify DNS propagation

2. **Service Won't Start**
   - Check logs: `journalctl -u secure-pool-server -f`
   - Verify environment file: `/opt/secure-pool-server/.env`
   - Check Redis is running: `systemctl status redis-server`

3. **Nginx Configuration Error**
   - Test config: `sudo nginx -t`
   - Check syntax in `/etc/nginx/sites-available/secure-pool-server`

4. **Permission Issues**
   - Verify ownership: `ls -la /opt/secure-pool-server/`
   - Fix permissions: `sudo chown -R poolserver:poolserver /opt/secure-pool-server/`

### Log Locations
- Installation log: `/var/log/secure-pool-server-install.log`
- Application logs: `/opt/secure-pool-server/logs/`
- System logs: `journalctl -u secure-pool-server`
- Nginx logs: `/var/log/nginx/`
- PM2 logs: `sudo -u poolserver pm2 logs`

## 🔒 Security Notes

- Private key is encrypted with AES-256-GCM
- Firewall configured to allow only necessary ports (22, 80, 443)
- Fail2ban active for intrusion prevention
- SSL certificate auto-renewal configured
- All services run with minimal privileges
- Regular security updates recommended

## 📞 Support

If you encounter issues:
1. Check the troubleshooting section above
2. Review installation logs
3. Verify all prerequisites are met
4. Check system resources (memory, disk space)

The installation creates a complete production environment with enterprise-level security features.
