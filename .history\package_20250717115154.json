{"name": "secure-pool-server", "version": "1.0.0", "description": "Production-grade secure server for blockchain pool management", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "lint": "eslint src/", "security-audit": "npm audit && snyk test", "pm2:start": "pm2 start ecosystem.config.js", "pm2:stop": "pm2 stop ecosystem.config.js", "pm2:restart": "pm2 restart ecosystem.config.js"}, "dependencies": {"express": "^4.18.2", "helmet": "^7.1.0", "cors": "^2.8.5", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "express-slow-down": "^2.0.1", "compression": "^1.7.4", "morgan": "^1.10.0", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "dotenv": "^16.3.1", "joi": "^17.11.0", "bcrypt": "^6.0.0", "crypto": "^1.0.1", "node-forge": "^1.3.1", "web3": "^4.16.0", "ethers": "^6.15.0", "axios": "^1.6.2", "express-mongo-sanitize": "^2.2.0", "xss": "^1.0.15", "hpp": "^0.2.3", "express-brute": "^1.0.1", "express-brute-redis": "^0.0.1", "redis": "^4.6.10", "uuid": "^11.1.0", "jsonwebtoken": "^9.0.2", "express-jwt": "^8.5.1", "node-rsa": "^1.1.1", "libp2p": "^2.9.0", "@libp2p/ping": "^2.0.36", "@libp2p/tcp": "^10.1.18", "@libp2p/websockets": "^9.2.18", "@libp2p/noise": "^12.0.1", "@chainsafe/libp2p-yamux": "^7.0.4", "@libp2p/circuit-relay-v2": "^3.2.23", "@libp2p/identify": "^3.0.38", "@multiformats/multiaddr": "^12.5.1"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.55.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-security": "^1.7.1", "snyk": "^1.1266.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["blockchain", "security", "pool", "web3", "express"], "author": "Secure Pool Team", "license": "MIT"}