module.exports = {
  apps: [
    {
      name: 'mainnet-pool-server',
      script: 'src/server.js',
      instances: 1, // Single instance to avoid port conflicts
      exec_mode: 'fork',

      // Environment
      env: {
        NODE_ENV: 'development',
        PORT: 3001
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 3001
      },
      
      // Logging
      log_file: 'logs/pm2-combined.log',
      out_file: 'logs/pm2-out.log',
      error_file: 'logs/pm2-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      
      // Process management
      max_memory_restart: '500M',
      min_uptime: '10s',
      max_restarts: 10,
      restart_delay: 4000,
      
      // Monitoring
      monitoring: true,
      pmx: true,
      
      // Auto restart on file changes (development only)
      watch: false,
      ignore_watch: [
        'node_modules',
        'logs',
        '.git',
        '*.log'
      ],
      
      // Advanced PM2 features
      kill_timeout: 5000,
      listen_timeout: 3000,
      shutdown_with_message: true,
      
      // Health check
      health_check_grace_period: 3000,
      
      // Source map support
      source_map_support: true,
      
      // Graceful shutdown
      kill_retry_time: 100,
      
      // Process title
      name: 'mainnet-pool-worker',
      
      // Cron restart (optional - restart every day at 2 AM)
      cron_restart: '0 2 * * *',
      
      // Exponential backoff restart delay
      exp_backoff_restart_delay: 100,
      
      // Environment variables
      env_file: '.env'
    }
  ],
  
  deploy: {
    production: {
      user: 'deploy',
      host: ['your-production-server.com'],
      ref: 'origin/main',
      repo: '**************:your-username/secure-pool-server.git',
      path: '/var/www/secure-pool-server',
      'pre-deploy-local': '',
      'post-deploy': 'npm install && pm2 reload ecosystem.config.js --env production',
      'pre-setup': '',
      'ssh_options': 'StrictHostKeyChecking=no'
    },
    
    staging: {
      user: 'deploy',
      host: ['your-staging-server.com'],
      ref: 'origin/develop',
      repo: '**************:your-username/secure-pool-server.git',
      path: '/var/www/secure-pool-server-staging',
      'post-deploy': 'npm install && pm2 reload ecosystem.config.js --env staging',
      'ssh_options': 'StrictHostKeyChecking=no'
    }
  }
};
