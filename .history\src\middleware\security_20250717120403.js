const helmet = require('helmet');
const cors = require('cors');
const rateLimit = require('express-rate-limit');
const slowDown = require('express-slow-down');
const mongoSanitize = require('express-mongo-sanitize');
const xss = require('xss');
const hpp = require('hpp');
// Removed vulnerable express-brute - using express-rate-limit instead
const redis = require('redis');
const { body, validationResult } = require('express-validator');
const config = require('../config/environment');
const logger = require('../utils/logger');

class SecurityMiddleware {
  constructor() {
    this.initializeRedis();
    this.initializeBruteForce();
  }

  async initializeRedis() {
    try {
      this.redisClient = redis.createClient({
        url: config.redis.url,
        password: config.redis.password
      });
      
      this.redisClient.on('error', (err) => {
        logger.error('Redis connection error:', err);
      });
      
      await this.redisClient.connect();
      logger.info('Redis connected successfully');
    } catch (error) {
      logger.error('Failed to connect to Redis:', error);
      // Fallback to memory store for brute force protection
      this.redisClient = null;
    }
  }

  initializeBruteForce() {
    const store = this.redisClient 
      ? new RedisStore({
          client: this.redisClient,
          prefix: 'bf:'
        })
      : new ExpressBrute.MemoryStore();

    this.bruteForce = new ExpressBrute(store, {
      freeRetries: 5,
      minWait: 5 * 60 * 1000, // 5 minutes
      maxWait: 60 * 60 * 1000, // 1 hour
      lifetime: 24 * 60 * 60, // 24 hours
      failCallback: (req, res, next, nextValidRequestDate) => {
        logger.warn(`Brute force attack detected from IP: ${req.ip}`);
        res.status(429).json({
          status: 'err',
          msg: 'Too many failed attempts. Please try again later.',
          retryAfter: Math.round((nextValidRequestDate.getTime() - Date.now()) / 1000)
        });
      }
    });
  }

  // Helmet security headers
  helmetConfig() {
    return helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          scriptSrc: ["'self'"],
          imgSrc: ["'self'", "data:", "https:"],
          connectSrc: ["'self'"],
          fontSrc: ["'self'"],
          objectSrc: ["'none'"],
          mediaSrc: ["'self'"],
          frameSrc: ["'none'"]
        }
      },
      crossOriginEmbedderPolicy: false,
      hsts: {
        maxAge: 31536000,
        includeSubDomains: true,
        preload: true
      }
    });
  }

  // CORS configuration
  corsConfig() {
    return cors({
      origin: (origin, callback) => {
        // Allow requests with no origin (mobile apps, etc.)
        if (!origin) return callback(null, true);
        
        const allowedOrigins = config.security.corsOrigin.split(',');
        
        if (allowedOrigins.includes('*') || allowedOrigins.includes(origin)) {
          return callback(null, true);
        }
        
        logger.warn(`CORS blocked request from origin: ${origin}`);
        return callback(new Error('Not allowed by CORS'));
      },
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
      maxAge: 86400 // 24 hours
    });
  }

  // Rate limiting
  rateLimitConfig() {
    return rateLimit({
      windowMs: config.rateLimit.windowMs,
      max: config.rateLimit.maxRequests,
      message: {
        status: 'err',
        msg: 'Too many requests from this IP, please try again later.'
      },
      standardHeaders: true,
      legacyHeaders: false,
      handler: (req, res) => {
        logger.warn(`Rate limit exceeded for IP: ${req.ip}`);
        res.status(429).json({
          status: 'err',
          msg: 'Too many requests from this IP, please try again later.'
        });
      },
      skip: (req) => {
        // Skip rate limiting for health checks
        return req.path === '/health';
      }
    });
  }

  // Slow down middleware
  slowDownConfig() {
    return slowDown({
      windowMs: config.rateLimit.windowMs,
      delayAfter: config.rateLimit.slowDownDelayAfter,
      delayMs: config.rateLimit.slowDownDelayMs,
      maxDelayMs: 20000, // Maximum delay of 20 seconds
      skipFailedRequests: false,
      skipSuccessfulRequests: false,
      onLimitReached: (req, res, options) => {
        logger.warn(`Slow down triggered for IP: ${req.ip}`);
      }
    });
  }

  // Input sanitization middleware
  sanitizeInput() {
    return (req, res, next) => {
      try {
        // Sanitize body
        if (req.body) {
          req.body = this.deepSanitize(req.body);
        }
        
        // Sanitize query parameters
        if (req.query) {
          req.query = this.deepSanitize(req.query);
        }
        
        // Sanitize URL parameters
        if (req.params) {
          req.params = this.deepSanitize(req.params);
        }
        
        next();
      } catch (error) {
        logger.error('Input sanitization error:', error);
        res.status(400).json({
          status: 'err',
          msg: 'Invalid input data'
        });
      }
    };
  }

  deepSanitize(obj) {
    if (typeof obj === 'string') {
      return xss(obj);
    }
    
    if (Array.isArray(obj)) {
      return obj.map(item => this.deepSanitize(item));
    }
    
    if (obj && typeof obj === 'object') {
      const sanitized = {};
      for (const [key, value] of Object.entries(obj)) {
        sanitized[key] = this.deepSanitize(value);
      }
      return sanitized;
    }
    
    return obj;
  }

  // Validation error handler
  handleValidationErrors() {
    return (req, res, next) => {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        logger.warn('Validation errors:', { 
          ip: req.ip, 
          errors: errors.array(),
          body: req.body 
        });
        
        return res.status(400).json({
          status: 'err',
          msg: 'Validation failed',
          errors: errors.array().map(err => ({
            field: err.param,
            message: err.msg
          }))
        });
      }
      next();
    };
  }

  // Join request validation rules
  joinValidationRules() {
    return [
      body('peerId')
        .isString()
        .isLength({ min: 1, max: 128 })
        .matches(/^[a-zA-Z0-9_-]+$/)
        .withMessage('Invalid peer ID format'),
      
      body('account')
        .isString()
        .matches(/^0x[a-fA-F0-9]{40}$/)
        .withMessage('Invalid Ethereum address format'),
      
      body('chain')
        .isString()
        .isIn(['base', 'skale'])
        .withMessage('Chain must be either "base" or "skale"'),
      
      body('poolId')
        .isInt({ min: 1 })
        .withMessage('Pool ID must be a positive integer')
    ];
  }

  // Security headers middleware
  securityHeaders() {
    return (req, res, next) => {
      res.setHeader('X-Content-Type-Options', 'nosniff');
      res.setHeader('X-Frame-Options', 'DENY');
      res.setHeader('X-XSS-Protection', '1; mode=block');
      res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
      res.setHeader('Permissions-Policy', 'geolocation=(), microphone=(), camera=()');
      next();
    };
  }

  // Request logging middleware
  requestLogger() {
    return (req, res, next) => {
      const startTime = Date.now();
      
      res.on('finish', () => {
        const duration = Date.now() - startTime;
        const logData = {
          method: req.method,
          url: req.url,
          ip: req.ip,
          userAgent: req.get('User-Agent'),
          statusCode: res.statusCode,
          duration: `${duration}ms`,
          contentLength: res.get('Content-Length') || 0
        };
        
        if (res.statusCode >= 400) {
          logger.warn('HTTP Error:', logData);
        } else {
          logger.info('HTTP Request:', logData);
        }
      });
      
      next();
    };
  }

  // Get brute force middleware
  getBruteForce() {
    return this.bruteForce.prevent;
  }
}

module.exports = new SecurityMiddleware();
