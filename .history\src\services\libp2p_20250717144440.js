const axios = require('axios');
const logger = require('../utils/logger');
const config = require('../config/environment');

class LibP2PService {
  constructor() {
    this.goServiceUrl = 'http://localhost:30001';
    this.pingTimeout = 15000; // 15 seconds timeout
    this.maxRetries = 3;
    this.isInitialized = false;
  }

  async initialize() {
    if (this.isInitialized) {
      return;
    }

    try {
      logger.info('Initializing LibP2P service (Go backend)...');

      // Test connection to Go service
      await this.healthCheck();
      this.isInitialized = true;

      logger.info('LibP2P service initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize libp2p service:', error);
      throw new Error('LibP2P service initialization failed - Go service not available');
    }
  }

  async shutdown() {
    if (this.isInitialized) {
      this.isInitialized = false;
      logger.info('LibP2P service shutdown');
    }
  }

  async healthCheck() {
    try {
      const response = await axios.get(`${this.goServiceUrl}/health`, {
        timeout: 5000
      });

      logger.info('Go LibP2P service health check passed', {
        status: response.data.status,
        peerId: response.data.peerId ? response.data.peerId.substring(0, 8) + '...' : 'unknown'
      });

      return response.data;
    } catch (error) {
      logger.error('Go LibP2P service health check failed:', error.message);
      throw new Error('Go LibP2P service not available');
    }
  }

  async pingPeer(peerId, retryCount = 0) {
    if (!this.isInitialized) {
      await this.initialize();
    }

    try {
      // Validate peer ID format
      if (!peerId || typeof peerId !== 'string') {
        throw new Error('Invalid peer ID provided');
      }

      // Clean peer ID (remove any prefixes)
      const cleanPeerId = peerId.replace(/^\/p2p\//, '').replace(/^12D3/, '12D3');

      logger.info('Attempting to ping peer via Go service', {
        peerId: cleanPeerId.substring(0, 8) + '...',
        attempt: retryCount + 1
      });

      const startTime = Date.now();

      // Call Go service
      const response = await axios.post(`${this.goServiceUrl}/ping`, {
        peerId: cleanPeerId
      }, {
        timeout: this.pingTimeout,
        headers: {
          'Content-Type': 'application/json'
        }
      });

      const result = response.data;
      const totalLatency = Date.now() - startTime;

      if (result.success) {
        logger.info('Ping successful via Go service', {
          peerId: cleanPeerId.substring(0, 8) + '...',
          latency: `${result.latency}ms`,
          totalLatency: `${totalLatency}ms`
        });

        return {
          success: true,
          peerId: cleanPeerId,
          latency: result.latency,
          totalLatency: totalLatency,
          timestamp: new Date().toISOString(),
          method: 'go-service'
        };
      } else {
        throw new Error(result.error || 'Ping failed');
      }

    } catch (error) {
      logger.warn('Ping attempt failed', {
        peerId: peerId ? peerId.substring(0, 8) + '...' : 'unknown',
        error: error.message,
        attempt: retryCount + 1
      });

      // Retry logic
      if (retryCount < this.maxRetries) {
        logger.info(`Retrying ping (${retryCount + 1}/${this.maxRetries})...`);
        await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1))); // Exponential backoff
        return this.pingPeer(peerId, retryCount + 1);
      }

      logger.error('Ping operation failed after all retries', {
        peerId: peerId ? peerId.substring(0, 8) + '...' : 'unknown',
        error: error.message,
        retryCount
      });

      return {
        success: false,
        peerId: peerId,
        error: error.message,
        timestamp: new Date().toISOString(),
        retryCount
      };
    }
  }

  // Alias for backward compatibility
  async validatePeerConnectivity(peerId) {
    const result = await this.pingPeer(peerId);
    return result.success;
  }
}

module.exports = new LibP2PService();
  async validatePeerConnectivity(peerId) {
    if (!this.isInitialized) {
      await this.initialize();
    }

    const startTime = Date.now();
    let lastError = null;

    try {
      // Validate peer ID format
      if (!peerId || typeof peerId !== 'string') {
        throw new Error('Invalid peer ID format');
      }

      // Clean peer ID (remove any prefixes)
      const cleanPeerId = peerId.replace(/^\/p2p\//, '').replace(/^12D3/, '12D3');
      
      // Construct the full multiaddr through the relay
      const targetMultiaddr = `${this.relayAddress}/p2p/${cleanPeerId}`;
      
      logger.info('Attempting to ping peer through relay', {
        peerId: cleanPeerId.substring(0, 8) + '...',
        relay: this.relayAddress,
        targetAddr: targetMultiaddr
      });

      // Parse the multiaddr
      let targetAddr;
      try {
        targetAddr = multiaddr(targetMultiaddr);
      } catch (error) {
        throw new Error(`Invalid multiaddr format: ${error.message}`);
      }

      // Attempt to ping with retries
      for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
        try {
          logger.debug(`Ping attempt ${attempt}/${this.maxRetries}`, {
            peerId: cleanPeerId.substring(0, 8) + '...',
            attempt
          });

          // Try to dial and ping the peer
          const connection = await this.node.dial(targetAddr);
          
          if (connection) {
            // Perform ping
            const pingResult = await this.node.services.ping.ping(connection.remotePeer);
            
            if (pingResult && pingResult > 0) {
              const duration = Date.now() - startTime;
              
              logger.info('Peer ping successful', {
                peerId: cleanPeerId.substring(0, 8) + '...',
                latency: pingResult,
                duration: `${duration}ms`,
                attempt
              });

              // Close the connection
              await connection.close();
              
              return true;
            }
          }
        } catch (error) {
          lastError = error;
          logger.warn(`Ping attempt ${attempt} failed`, {
            peerId: cleanPeerId.substring(0, 8) + '...',
            attempt,
            error: error.message
          });

          // Wait before retry (exponential backoff)
          if (attempt < this.maxRetries) {
            const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000);
            await new Promise(resolve => setTimeout(resolve, delay));
          }
        }
      }

      // All attempts failed
      const duration = Date.now() - startTime;
      
      logger.warn('Peer ping failed after all attempts', {
        peerId: cleanPeerId.substring(0, 8) + '...',
        attempts: this.maxRetries,
        duration: `${duration}ms`,
        lastError: lastError?.message
      });

      return false;

    } catch (error) {
      const duration = Date.now() - startTime;
      
      logger.error('Peer connectivity validation error', {
        peerId: peerId.substring(0, 8) + '...',
        error: error.message,
        duration: `${duration}ms`
      });

      return false;
    }
  }

  /**
   * Health check for the libp2p service
   * @returns {Promise<Object>} - Health status
   */
  async healthCheck() {
    try {
      if (!this.isInitialized) {
        return {
          status: 'unhealthy',
          error: 'LibP2P node not initialized'
        };
      }

      const connections = this.node.getConnections();
      const peers = this.node.getPeers();

      return {
        status: 'healthy',
        peerId: this.node.peerId.toString(),
        connections: connections.length,
        peers: peers.length,
        addresses: this.node.getMultiaddrs().map(addr => addr.toString())
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error.message
      };
    }
  }

  /**
   * Get connection statistics
   * @returns {Object} - Connection statistics
   */
  getStats() {
    if (!this.isInitialized || !this.node) {
      return {
        initialized: false,
        connections: 0,
        peers: 0
      };
    }

    try {
      const connections = this.node.getConnections();
      const peers = this.node.getPeers();

      return {
        initialized: true,
        peerId: this.node.peerId.toString(),
        connections: connections.length,
        peers: peers.length,
        addresses: this.node.getMultiaddrs().map(addr => addr.toString()),
        connectionDetails: connections.map(conn => ({
          remotePeer: conn.remotePeer.toString(),
          status: conn.status,
          direction: conn.direction,
          timeline: conn.timeline
        }))
      };
    } catch (error) {
      logger.error('Error getting libp2p stats:', error);
      return {
        initialized: true,
        error: error.message
      };
    }
  }

  /**
   * Force reconnection to relay
   * @returns {Promise<boolean>} - Success status
   */
  async reconnectToRelay() {
    try {
      logger.info('Attempting to reconnect to relay...');
      
      const relayAddr = multiaddr(this.relayAddress);
      const connection = await this.node.dial(relayAddr);
      
      if (connection) {
        logger.info('Successfully reconnected to relay');
        return true;
      }
      
      return false;
    } catch (error) {
      logger.error('Failed to reconnect to relay:', error);
      return false;
    }
  }
}

module.exports = new LibP2PService();
