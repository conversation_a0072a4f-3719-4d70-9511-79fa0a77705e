#!/bin/bash

set -e

APP_USER="mainnet"
INSTALL_DIR="/opt/mainnet"
PM2_HOME="$INSTALL_DIR/.pm2"
SERVICE_NAME="mainnet-pool-server"
LOG_FILE="/var/log/secure-pool-server-install.log"
NGINX_SITES_DIR="/etc/nginx/sites-available"
NGINX_ENABLED_DIR="/etc/nginx/sites-enabled"

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

print_status() {
    echo -e "${YELLOW}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[OK]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Confirm with user
read -p "This will permanently delete the 'mainnet' user and all associated files. Continue? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Aborted."
    exit 1
fi

print_status "Stopping services..."

# Stop systemd service if exists
if systemctl list-units | grep -q "$SERVICE_NAME"; then
    systemctl stop "$SERVICE_NAME" || true
    systemctl disable "$SERVICE_NAME" || true
    rm -f "/etc/systemd/system/$SERVICE_NAME.service"
    systemctl daemon-reexec
    print_success "Systemd service removed"
fi

# Kill PM2 processes under mainnet
if id "$APP_USER" &>/dev/null; then
    sudo -u "$APP_USER" bash -c "export PM2_HOME=$PM2_HOME && pm2 kill" || true
    rm -rf "$PM2_HOME"
    print_success "PM2 processes and config removed"
fi

# Remove Nginx config
rm -f "$NGINX_SITES_DIR/$SERVICE_NAME"
rm -f "$NGINX_ENABLED_DIR/$SERVICE_NAME"
print_success "Nginx config removed (reload nginx manually if needed)"

# Remove fail2ban jail config
rm -f "/etc/fail2ban/jail.d/$SERVICE_NAME.conf"

# Remove app files
rm -rf "$INSTALL_DIR"
print_success "Deleted $INSTALL_DIR"

# Remove the user and its group
if id "$APP_USER" &>/dev/null; then
    userdel -r "$APP_USER"
    print_success "Deleted user '$APP_USER' and home"
fi

# Clean up leftover logs
rm -f "$LOG_FILE"

print_success "All mainnet files and user removed"
