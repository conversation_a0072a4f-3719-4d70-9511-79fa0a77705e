const crypto = require('crypto');
const NodeRSA = require('node-rsa');
const bcrypt = require('bcrypt');
const Joi = require('joi');

class SecurityManager {
  constructor() {
    this.algorithm = 'aes-256-gcm';
    this.keyLength = 32;
    this.ivLength = 16;
    this.tagLength = 16;
    this.saltLength = 32;
    this.iterations = 100000;
    
    // Validate environment variables on initialization
    this.validateEnvironment();
    
    // Initialize encryption key from environment
    this.initializeEncryptionKey();
  }

  validateEnvironment() {
    const schema = Joi.object({
      NODE_ENV: Joi.string().valid('development', 'production', 'test').required(),
      ENCRYPTION_KEY: Joi.string().length(64).required(), // 32 bytes in hex
      MASTER_PASSWORD: Joi.string().min(16).required(),
      JWT_SECRET: Joi.string().min(32).required(),
      ENCRYPTED_PRIVATE_KEY: Joi.string().required(),
      PORT: Joi.number().port().default(3000),
      HOST: Joi.string().default('0.0.0.0')
    });

    const { error } = schema.validate(process.env);
    if (error) {
      throw new Error(`Environment validation failed: ${error.details[0].message}`);
    }
  }

  initializeEncryptionKey() {
    try {
      // Derive key from master password and fixed salt for consistency
      const salt = crypto.createHash('sha256').update('POOL_SERVER_SALT').digest();
      this.masterKey = crypto.pbkdf2Sync(
        process.env.MASTER_PASSWORD,
        salt,
        this.iterations,
        this.keyLength,
        'sha512'
      );
    } catch (error) {
      throw new Error('Failed to initialize encryption key');
    }
  }

  /**
   * Encrypts sensitive data using AES-256-GCM
   * @param {string} plaintext - Data to encrypt
   * @returns {string} - Base64 encoded encrypted data with IV and tag
   */
  encrypt(plaintext) {
    try {
      const iv = crypto.randomBytes(this.ivLength);
      const cipher = crypto.createCipher(this.algorithm, this.masterKey, { iv });
      
      let encrypted = cipher.update(plaintext, 'utf8', 'hex');
      encrypted += cipher.final('hex');
      
      const tag = cipher.getAuthTag();
      
      // Combine IV, tag, and encrypted data
      const combined = Buffer.concat([iv, tag, Buffer.from(encrypted, 'hex')]);
      return combined.toString('base64');
    } catch (error) {
      throw new Error('Encryption failed');
    }
  }

  /**
   * Decrypts data encrypted with encrypt method
   * @param {string} encryptedData - Base64 encoded encrypted data
   * @returns {string} - Decrypted plaintext
   */
  decrypt(encryptedData) {
    try {
      const combined = Buffer.from(encryptedData, 'base64');
      
      const iv = combined.slice(0, this.ivLength);
      const tag = combined.slice(this.ivLength, this.ivLength + this.tagLength);
      const encrypted = combined.slice(this.ivLength + this.tagLength);
      
      const decipher = crypto.createDecipher(this.algorithm, this.masterKey, { iv });
      decipher.setAuthTag(tag);
      
      let decrypted = decipher.update(encrypted, null, 'utf8');
      decrypted += decipher.final('utf8');
      
      return decrypted;
    } catch (error) {
      throw new Error('Decryption failed');
    }
  }

  /**
   * Securely retrieves and decrypts the private key
   * @returns {string} - Decrypted private key
   */
  getPrivateKey() {
    try {
      const encryptedKey = process.env.ENCRYPTED_PRIVATE_KEY;
      if (!encryptedKey) {
        throw new Error('Private key not found in environment');
      }
      
      return this.decrypt(encryptedKey);
    } catch (error) {
      throw new Error('Failed to retrieve private key');
    }
  }

  /**
   * Encrypts a private key for storage
   * @param {string} privateKey - Private key to encrypt
   * @returns {string} - Encrypted private key for storage
   */
  encryptPrivateKey(privateKey) {
    if (!privateKey || typeof privateKey !== 'string') {
      throw new Error('Invalid private key provided');
    }
    
    // Validate private key format (basic check)
    if (!privateKey.startsWith('0x') || privateKey.length !== 66) {
      throw new Error('Invalid private key format');
    }
    
    return this.encrypt(privateKey);
  }

  /**
   * Generates a secure random string
   * @param {number} length - Length of random string
   * @returns {string} - Secure random string
   */
  generateSecureRandom(length = 32) {
    return crypto.randomBytes(length).toString('hex');
  }

  /**
   * Hashes a password securely
   * @param {string} password - Password to hash
   * @returns {Promise<string>} - Hashed password
   */
  async hashPassword(password) {
    const saltRounds = 12;
    return bcrypt.hash(password, saltRounds);
  }

  /**
   * Verifies a password against a hash
   * @param {string} password - Password to verify
   * @param {string} hash - Hash to verify against
   * @returns {Promise<boolean>} - Verification result
   */
  async verifyPassword(password, hash) {
    return bcrypt.compare(password, hash);
  }

  /**
   * Sanitizes input to prevent injection attacks
   * @param {any} input - Input to sanitize
   * @returns {any} - Sanitized input
   */
  sanitizeInput(input) {
    if (typeof input === 'string') {
      // Remove potential script tags and dangerous characters
      return input
        .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
        .replace(/[<>'"&]/g, (char) => {
          const entities = {
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#x27;',
            '&': '&amp;'
          };
          return entities[char];
        });
    }
    return input;
  }

  /**
   * Validates Ethereum address format
   * @param {string} address - Address to validate
   * @returns {boolean} - Validation result
   */
  isValidEthereumAddress(address) {
    return /^0x[a-fA-F0-9]{40}$/.test(address);
  }

  /**
   * Validates peer ID format
   * @param {string} peerId - Peer ID to validate
   * @returns {boolean} - Validation result
   */
  isValidPeerId(peerId) {
    // Basic validation for peer ID (adjust based on your specific format)
    return typeof peerId === 'string' && peerId.length > 0 && peerId.length <= 128;
  }
}

module.exports = new SecurityManager();
