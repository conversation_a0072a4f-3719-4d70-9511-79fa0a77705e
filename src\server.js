const express = require('express');
const compression = require('compression');
const mongoSanitize = require('express-mongo-sanitize');
const hpp = require('hpp');
const config = require('./config/environment');
const logger = require('./utils/logger');
const securityMiddleware = require('./middleware/security');
const libp2pService = require('./services/libp2p');

class SecurePoolServer {
  constructor() {
    this.app = express();
    this.server = null;
    this.isShuttingDown = false;
    
    this.initializeMiddleware();
    this.initializeRoutes();
    this.initializeErrorHandling();
    this.setupGracefulShutdown();
  }

  initializeMiddleware() {
    // Trust proxy for accurate IP addresses
    this.app.set('trust proxy', config.security.trustedProxies);

    // Security headers
    this.app.use(securityMiddleware.helmetConfig());
    this.app.use(securityMiddleware.securityHeaders());

    // CORS
    this.app.use(securityMiddleware.corsConfig());

    // Rate limiting and slow down
    this.app.use(securityMiddleware.rateLimitConfig());
    this.app.use(securityMiddleware.slowDownConfig());

    // Body parsing with size limits
    this.app.use(express.json({ 
      limit: '10mb',
      strict: true
    }));
    this.app.use(express.urlencoded({ 
      extended: true, 
      limit: '10mb' 
    }));

    // Compression
    this.app.use(compression({
      level: 6,
      threshold: 1024,
      filter: (req, res) => {
        if (req.headers['x-no-compression']) {
          return false;
        }
        return compression.filter(req, res);
      }
    }));

    // Input sanitization
    this.app.use(mongoSanitize({
      replaceWith: '_',
      onSanitize: ({ req, key }) => {
        logger.security.suspiciousActivity({
          type: 'mongo_injection_attempt',
          ip: req.ip,
          key,
          userAgent: req.get('User-Agent')
        });
      }
    }));

    this.app.use(hpp({
      whitelist: ['poolId'] // Allow duplicate poolId parameters if needed
    }));

    this.app.use(securityMiddleware.sanitizeInput());

    // Request logging
    if (config.logging.enableRequestLogging) {
      this.app.use(securityMiddleware.requestLogger());
    }

    // Health check middleware (before other routes)
    this.app.use('/health', (req, res, next) => {
      if (this.isShuttingDown) {
        return res.status(503).json({
          status: 'err',
          msg: 'Server is shutting down'
        });
      }
      next();
    });
  }

  initializeRoutes() {
    // Health check endpoint
    this.app.get('/health', async (req, res) => {
      try {
        // Import blockchain service here to avoid circular dependencies
        const BlockchainService = require('./services/blockchain');
        const healthData = await BlockchainService.healthCheck();

        res.status(200).json({
          status: 'ok',
          timestamp: new Date().toISOString(),
          uptime: process.uptime(),
          environment: config.server.env,
          version: process.env.npm_package_version || '1.0.0',
          services: healthData
        });
      } catch (error) {
        logger.error('Health check error:', error);
        res.status(503).json({
          status: 'err',
          msg: 'Health check failed',
          timestamp: new Date().toISOString()
        });
      }
    });

    // LibP2P status endpoint (for debugging)
    this.app.get('/libp2p/status', async (req, res) => {
      try {
        const stats = libp2pService.getStats();
        const health = await libp2pService.healthCheck();

        res.status(200).json({
          status: 'ok',
          timestamp: new Date().toISOString(),
          libp2p: {
            ...stats,
            health
          }
        });
      } catch (error) {
        logger.error('LibP2P status check error:', error);
        res.status(503).json({
          status: 'err',
          msg: 'LibP2P status check failed',
          error: error.message
        });
      }
    });

    // Main page - unauthorized message
    this.app.get('/', (req, res) => {
      logger.info('Unauthorized access attempt to main page', {
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });
      
      res.status(401).json({
        status: 'err',
        msg: 'You are not authorized'
      });
    });

    // Join endpoint with comprehensive security
    this.app.post('/join',
      securityMiddleware.getBruteForce(),
      securityMiddleware.joinValidationRules(),
      securityMiddleware.handleValidationErrors(),
      async (req, res) => {
        try {
          const { peerId, account, chain, poolId } = req.body;
          
          logger.info('Join request received', {
            peerId: peerId.substring(0, 8) + '...', // Log partial peer ID for privacy
            account: account.substring(0, 6) + '...' + account.substring(38), // Log partial address
            chain,
            poolId,
            ip: req.ip
          });

          // Import blockchain service here to avoid circular dependencies
          const BlockchainService = require('./services/blockchain');
          const result = await BlockchainService.addMemberToPool({
            peerId,
            account,
            chain,
            poolId
          });

          if (result.success) {
            logger.security.transactionSuccess({
              poolId,
              account: account.substring(0, 6) + '...' + account.substring(38),
              chain,
              transactionHash: result.transactionHash
            });

            res.status(200).json({
              status: 'ok',
              msg: 'joined',
              transactionHash: result.transactionHash
            });
          } else {
            logger.warn('Join request failed', {
              poolId,
              account: account.substring(0, 6) + '...' + account.substring(38),
              chain,
              error: result.error,
              ip: req.ip
            });

            res.status(400).json({
              status: 'err',
              msg: result.error || 'Failed to join pool'
            });
          }
        } catch (error) {
          logger.error('Join endpoint error', {
            error: error.message,
            stack: error.stack,
            ip: req.ip,
            body: req.body
          });

          res.status(500).json({
            status: 'err',
            msg: 'Internal server error'
          });
        }
      }
    );

    // Catch-all route for undefined endpoints
    this.app.use('*', (req, res) => {
      logger.security.accessDenied({
        path: req.originalUrl,
        method: req.method,
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });

      res.status(404).json({
        status: 'err',
        msg: 'Endpoint not found'
      });
    });
  }

  initializeErrorHandling() {
    // Global error handler
    this.app.use((error, req, res, next) => {
      logger.error('Unhandled error', {
        error: error.message,
        stack: error.stack,
        url: req.url,
        method: req.method,
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });

      // Don't leak error details in production
      const message = config.server.isProduction 
        ? 'Internal server error' 
        : error.message;

      res.status(error.status || 500).json({
        status: 'err',
        msg: message
      });
    });
  }

  setupGracefulShutdown() {
    const gracefulShutdown = async (signal) => {
      logger.info(`Received ${signal}. Starting graceful shutdown...`);
      this.isShuttingDown = true;

      try {
        // Shutdown libp2p service first
        logger.info('Shutting down libp2p service...');
        await libp2pService.shutdown();

        if (this.server) {
          this.server.close((err) => {
            if (err) {
              logger.error('Error during server shutdown:', err);
              process.exit(1);
            }

            logger.info('Server closed successfully');
            process.exit(0);
          });

          // Force shutdown after 30 seconds
          setTimeout(() => {
            logger.error('Forced shutdown after timeout');
            process.exit(1);
          }, 30000);
        } else {
          process.exit(0);
        }
      } catch (error) {
        logger.error('Error during graceful shutdown:', error);
        process.exit(1);
      }
    };

    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));
    
    process.on('uncaughtException', (error) => {
      logger.error('Uncaught Exception:', error);
      gracefulShutdown('uncaughtException');
    });

    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
      gracefulShutdown('unhandledRejection');
    });
  }

  start() {
    try {
      this.server = this.app.listen(config.server.port, config.server.host, () => {
        logger.info(`Secure Pool Server started`, {
          port: config.server.port,
          host: config.server.host,
          environment: config.server.env,
          nodeVersion: process.version,
          pid: process.pid
        });
      });

      this.server.on('error', (error) => {
        if (error.code === 'EADDRINUSE') {
          logger.error(`Port ${config.server.port} is already in use`);
        } else {
          logger.error('Server error:', error);
        }
        process.exit(1);
      });

    } catch (error) {
      logger.error('Failed to start server:', error);
      process.exit(1);
    }
  }
}

// Start server if this file is run directly
if (require.main === module) {
  const server = new SecurePoolServer();
  server.start();
}

module.exports = SecurePoolServer;
