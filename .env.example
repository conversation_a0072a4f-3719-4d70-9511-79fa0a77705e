# Server Configuration
NODE_ENV=production
PORT=3000
HOST=0.0.0.0

# Security Configuration
ENCRYPTION_KEY=your-32-character-encryption-key-here
MASTER_PASSWORD=your-master-password-for-key-derivation
JWT_SECRET=your-jwt-secret-key-here
SESSION_SECRET=your-session-secret-here

# Encrypted Private Key (Base64 encoded encrypted private key)
ENCRYPTED_PRIVATE_KEY=your-encrypted-private-key-here

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
SLOW_DOWN_DELAY_AFTER=50
SLOW_DOWN_DELAY_MS=500

# Blockchain Configuration
BASE_RPC_URL=https://mainnet.base.org
SKALE_RPC_URL=https://mainnet.skalenodes.com/v1/elated-tan-skat
POOL_STORAGE_CONTRACT=0xf293A6902662DcB09E310254A5e418cb28D71b6b

# Gas Configuration
GAS_LIMIT=500000
GAS_PRICE_MULTIPLIER=1.2
MAX_GAS_PRICE=50000000000

# Redis Configuration (for rate limiting and caching)
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=your-redis-password

# Monitoring and Logging
LOG_LEVEL=info
LOG_MAX_SIZE=20m
LOG_MAX_FILES=14d
ENABLE_REQUEST_LOGGING=true

# Security Headers
CORS_ORIGIN=https://your-frontend-domain.com
TRUSTED_PROXIES=1

# Health Check
HEALTH_CHECK_INTERVAL=30000
